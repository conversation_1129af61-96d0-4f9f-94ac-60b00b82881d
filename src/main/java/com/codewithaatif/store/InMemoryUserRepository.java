package com.codewithaatif.store;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Repository;

@Repository
public class InMemoryUserRepository implements UserRepositoryTest {

    private final Map<String, UserTest> users = new HashMap<>();

    @Override
    public void save(UserTest user) {
        System.out.println("User saved: " + user);
        users.put(user.getEmail(), user);
    }

    @Override
    public UserTest findByEmail(String email) {
       return users.getOrDefault(email, null);
    }
}