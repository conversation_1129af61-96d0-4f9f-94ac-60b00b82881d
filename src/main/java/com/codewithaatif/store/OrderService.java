

package com.codewithaatif.store;

import org.springframework.beans.factory.annotation.Autowired;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

// @Service
public class OrderService {

    private PaymentService paymentService;
    

    @Autowired
    public OrderService(PaymentService paymentService) {
        System.out.println("OrderService created");
        this.paymentService = paymentService;
    }

    @PreDestroy
    public void cleanup() {
        System.out.println("OrderService PreDestroy");
    }

    @PostConstruct
    public void init() {
        System.out.println("OrderService PostConstruct");
    }

    public void placeOrder() {
        paymentService.processPayment(10);
        System.out.println("Order placed");
    }
}
