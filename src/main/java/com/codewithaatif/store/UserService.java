
package com.codewithaatif.store;

import org.springframework.stereotype.Service;

@Service
public class UserService {
    private UserRepositoryTest userRepository;
    private NotificationService notificationService;

    public UserService(UserRepositoryTest userRepository, NotificationService notificationService) {
        this.userRepository = userRepository;
        this.notificationService = notificationService;
    }
 

    public void registerUser(UserTest user) {
        if(userRepository.findByEmail(user.getEmail()) != null) {
            throw new IllegalArgumentException("User already exists");
        }

        userRepository.save(user);
        notificationService.send( "User registered", user.getEmail());
    }
}

