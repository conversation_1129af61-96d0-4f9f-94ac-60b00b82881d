package com.codewithaatif.store.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "addresses")
public class Address {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(nullable=false, name = "street")
    private String street;

    @Column(nullable=false, name = "city")
    private String city;

    @Column(nullable=false, name = "state")
    private String state;

    @Column(nullable=false, name = "zip")
    private String zip;

    // @Column(nullable=false, name = "user_id")
    // private Long userId;
    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;


}
