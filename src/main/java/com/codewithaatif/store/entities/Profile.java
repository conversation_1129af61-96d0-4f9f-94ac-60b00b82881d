package com.codewithaatif.store.entities;

import java.time.LocalDate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "profiles")
public class Profile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(nullable=false, name = "bio")
    private String bio;

    @Column(nullable=false, name = "phone_number")
    private Long phoneNumber;

    @Column(nullable=false, name = "date_of_birth")
    private LocalDate dateOfBirth;

    @Column(nullable=false, name = "loyalty_points")
    private Integer loyaltyPoints;
}
