package com.codewithaatif.store;

import org.springframework.beans.factory.annotation.Value;

public class EmailNotificationService implements NotificationService {

    @Value("${mail.host}")
    private String host;

    @Value("${mail.port}")
    private String port;


    @Override
    public void send(String message, String recipientEmail) {
        System.out.println("Sending email notification: " + message);
        System.out.println("Recipient email: " + recipientEmail);
        System.out.println("Host: " + host);
        System.out.println("Port: " + port);
    }
}