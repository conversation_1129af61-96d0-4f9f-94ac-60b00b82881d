package com.codewithaatif.store;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class AppConfig {

    @Bean
    public PaymentService stripe() {
        return new StripePaymentService();
    }

    @Bean
    public PaymentService payPal() {
        return new PayPalPaymentService();
    }


    @Value("${app.defaultPaymentService}")
    private String defaultPaymentService;

    @Bean
    public OrderService orderService() {

        if(defaultPaymentService.equals("stripe")){
            return new OrderService(stripe());
        }
        return new OrderService(payPal());
        
    }

    @Bean
    @Primary
    public NotificationService notificationService() {
        return new EmailNotificationService();
    }

    @Bean
    public NotificationService smsNotificationService() {
        return new SMSNotificationService();
    }

    @Value("${app.defaultCommService}")
    private String defaultCommService;

    @Bean
    public NotificationManager notificationManager() {
        if(defaultCommService.equals("email")){
            return new NotificationManager(notificationService());
        }
        return new NotificationManager(smsNotificationService());
    }

}
