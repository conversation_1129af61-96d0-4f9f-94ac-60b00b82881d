package com.codewithaatif.store;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;

import com.codewithaatif.store.entities.Address;
import com.codewithaatif.store.entities.Profile;
import com.codewithaatif.store.entities.User;
import com.codewithaatif.store.repositories.UserRepository;

@SpringBootApplication
public class StoreApplication {

	public static void main(String[] args) {

		ApplicationContext context = SpringApplication.run(StoreApplication.class, args);
		var repository = context.getBean(UserRepository.class);
		
		
		
		var user = new User();
		user.setName("Aatif");
		user.setEmail("<EMAIL>");
		user.setPassword("password");

		user.addTag("testTag");

		repository.save(user);

		var profile = new Profile();
		profile.setUser(user);
		profile.setBio("testing");

		var addresses = new Address();
		addresses.setStreet("123 Main St");
		addresses.setCity("New York");
		addresses.setZip("10001");

		user.addAddress(addresses);

		System.out.println(user);
		// user.getAddresses().add(addresses);
		// addresses.setUser(user);




		// var orderService = context.getBean(OrderService.class);
		// orderService.placeOrder();

		// var notificationService = context.getBean(NotificationManager.class);
		// notificationService.sendNotification();

		//var userService = context.getBean(UserService.class);
		// var user1 = new UserTest(1L, "<EMAIL>", "test1", "Test1");
		// userService.registerUser(user1);
		
		// var orderService = new OrderService(new PayPalPaymentService());

		// var resource = context.getBean(HeavyResource.class);
		
	}

}
