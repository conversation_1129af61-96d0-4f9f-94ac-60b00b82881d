package com.codewithaatif.store;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;

import com.codewithaatif.store.entities.Address;
import com.codewithaatif.store.entities.User;

@SpringBootApplication
public class StoreApplication {

	public static void main(String[] args) {

		ApplicationContext context = SpringApplication.run(StoreApplication.class, args);
		var user = new User();
		user.setName("Aatif");
		user.setEmail("<EMAIL>");
		user.setPassword("password");

		var addresses = new Address();
		addresses.setStreet("123 Main St");
		addresses.setCity("New York");
		addresses.setState("NY");
		addresses.setZip("10001");

		user.addAddress(addresses);

		System.out.println(user);
		// user.getAddresses().add(addresses);
		// addresses.setUser(user);




		// var orderService = context.getBean(OrderService.class);
		// orderService.placeOrder();

		// var notificationService = context.getBean(NotificationManager.class);
		// notificationService.sendNotification();

		//var userService = context.getBean(UserService.class);
		// var user1 = new UserTest(1L, "<EMAIL>", "test1", "Test1");
		// userService.registerUser(user1);
		
		// var orderService = new OrderService(new PayPalPaymentService());

		// var resource = context.getBean(HeavyResource.class);
		
	}

}
